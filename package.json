{"name": "layeredge-edgen-community", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:seed": "tsx scripts/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@headlessui/react": "^2.0.0", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.7.1", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^3.0.0", "framer-motion": "^11.0.0", "next": "15.3.2", "next-auth": "5.0.0-beta.25", "prisma": "^5.7.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}