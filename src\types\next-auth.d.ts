import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      xUsername?: string | null
      xUserId?: string | null
      totalPoints?: number
      rank?: number | null
    }
  }

  interface User {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
    xUsername?: string | null
    xUserId?: string | null
    totalPoints?: number
    rank?: number | null
  }
}
